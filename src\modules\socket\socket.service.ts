import { Injectable } from '@nestjs/common';
import { SocketGateway } from './socket.gateway';
import { EAiGenPromptByDescriptionType } from 'src/common/constant';

@Injectable()
export class SocketService {
  constructor(private readonly socketGateway: SocketGateway) { }

  emitUpdateChapter(chapterId: number): void {
    this.socketGateway.emitUpdateChapter(chapterId);
  }

  emitSyntheticData(syntheticDataId: number): void {
    this.socketGateway.emitSyntheticData(syntheticDataId);
  }

  emitPromptByDescription(
    cutId: number,
    typePrompt: EAiGenPromptByDescriptionType,
    prompt: string,
  ): void {
    this.socketGateway.emitPromptByDescription(cutId, typePrompt, prompt);
  }

  emitMergePreviewImages(chapterId: number, images: string[]): void {
    this.socketGateway.emitMergePreviewImages(chapterId, images);
  }
}
